{"version": 3, "file": "cross-fetch.js", "sources": ["../node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["var support = {\n  searchParams: 'URLSearchParams' in self,\n  iterable: 'Symbol' in self && 'iterator' in Symbol,\n  blob:\n    'FileReader' in self &&\n    'Blob' in self &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in self,\n  arrayBuffer: 'ArrayBuffer' in self\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~]/i.test(name)) {\n    throw new TypeError('Invalid character in header field name')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsText(blob)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    this._bodyInit = body\n    if (!body) {\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n\n    this.arrayBuffer = function() {\n      if (this._bodyArrayBuffer) {\n        return consumed(this) || Promise.resolve(this._bodyArrayBuffer)\n      } else {\n        return this.blob().then(readBlobAsArrayBuffer)\n      }\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  preProcessedHeaders.split(/\\r?\\n/).forEach(function(line) {\n    var parts = line.split(':')\n    var key = parts.shift().trim()\n    if (key) {\n      var value = parts.join(':').trim()\n      headers.append(key, value)\n    }\n  })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = 'statusText' in options ? options.statusText : 'OK'\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 0, statusText: ''})\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = self.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        status: xhr.status,\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      resolve(new Response(body, options))\n    }\n\n    xhr.onerror = function() {\n      reject(new TypeError('Network request failed'))\n    }\n\n    xhr.ontimeout = function() {\n      reject(new TypeError('Network request failed'))\n    }\n\n    xhr.onabort = function() {\n      reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    xhr.open(request.method, request.url, true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr && support.blob) {\n      xhr.responseType = 'blob'\n    }\n\n    request.headers.forEach(function(value, name) {\n      xhr.setRequestHeader(name, value)\n    })\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!self.fetch) {\n  self.fetch = fetch\n  self.Headers = Headers\n  self.Request = Request\n  self.Response = Response\n}\n"], "names": ["support", "self", "Symbol", "Blob", "e", "viewClasses", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "indexOf", "Object", "prototype", "toString", "call", "normalizeName", "name", "String", "test", "TypeError", "toLowerCase", "normalizeValue", "value", "iteratorFor", "items", "iterator", "next", "shift", "done", "undefined", "Headers", "headers", "this", "map", "for<PERSON>ach", "append", "Array", "isArray", "header", "getOwnPropertyNames", "consumed", "body", "bodyUsed", "Promise", "reject", "fileReaderReady", "reader", "resolve", "onload", "result", "onerror", "error", "readBlobAsArrayBuffer", "blob", "FileReader", "promise", "readAsA<PERSON>y<PERSON><PERSON>er", "bufferClone", "buf", "slice", "view", "Uint8Array", "byteLength", "set", "buffer", "Body", "_initBody", "_bodyInit", "_bodyText", "isPrototypeOf", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "DataView", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "type", "rejected", "Error", "arrayBuffer", "then", "text", "readAsText", "chars", "length", "i", "fromCharCode", "join", "readArrayBufferAsText", "formData", "decode", "json", "JSON", "parse", "oldValue", "has", "hasOwnProperty", "callback", "thisArg", "keys", "push", "values", "entries", "methods", "Request", "input", "options", "method", "upcased", "url", "credentials", "mode", "signal", "toUpperCase", "referrer", "form", "trim", "split", "bytes", "replace", "decodeURIComponent", "Response", "bodyInit", "status", "ok", "statusText", "clone", "response", "redirectStatuses", "redirect", "RangeError", "location", "DOMException", "err", "message", "stack", "create", "constructor", "fetch", "init", "request", "aborted", "xhr", "XMLHttpRequest", "abortXhr", "abort", "rawHeaders", "getAllResponseHeaders", "line", "parts", "key", "responseURL", "responseText", "ontimeout", "<PERSON>ab<PERSON>", "open", "withCredentials", "responseType", "setRequestHeader", "addEventListener", "onreadystatechange", "readyState", "removeEventListener", "send", "polyfill"], "mappings": "0BAAA,IAAIA,EACY,oBAAqBC,EADjCD,EAEQ,WAAYC,GAAQ,aAAcC,OAF1CF,EAIA,eAAgBC,GAChB,SAAUA,GACV,WACE,IAEE,OADA,IAAIE,MACG,EACP,MAAOC,GACP,OAAO,GALX,GANAJ,EAcQ,aAAcC,EAdtBD,EAeW,gBAAiBC,EAOhC,GAAID,EACF,IAAIK,EAAc,CAChB,qBACA,sBACA,6BACA,sBACA,uBACA,sBACA,uBACA,wBACA,yBAGEC,EACFC,YAAYC,QACZ,SAASC,GACP,OAAOA,GAAOJ,EAAYK,QAAQC,OAAOC,UAAUC,SAASC,KAAKL,KAAS,GAIhF,SAASM,EAAcC,GAIrB,GAHoB,iBAATA,IACTA,EAAOC,OAAOD,IAEZ,4BAA4BE,KAAKF,GACnC,MAAM,IAAIG,UAAU,0CAEtB,OAAOH,EAAKI,cAGd,SAASC,EAAeC,GAItB,MAHqB,iBAAVA,IACTA,EAAQL,OAAOK,IAEVA,EAIT,SAASC,EAAYC,GACnB,IAAIC,EAAW,CACbC,KAAM,WACJ,IAAIJ,EAAQE,EAAMG,QAClB,MAAO,CAACC,UAAgBC,IAAVP,EAAqBA,MAAOA,KAU9C,OANItB,IACFyB,EAASvB,OAAOuB,UAAY,WAC1B,OAAOA,IAIJA,EAGF,SAASK,EAAQC,GACtBC,KAAKC,IAAM,GAEPF,aAAmBD,EACrBC,EAAQG,SAAQ,SAASZ,EAAON,GAC9BgB,KAAKG,OAAOnB,EAAMM,KACjBU,MACMI,MAAMC,QAAQN,GACvBA,EAAQG,SAAQ,SAASI,GACvBN,KAAKG,OAAOG,EAAO,GAAIA,EAAO,MAC7BN,MACMD,GACTpB,OAAO4B,oBAAoBR,GAASG,SAAQ,SAASlB,GACnDgB,KAAKG,OAAOnB,EAAMe,EAAQf,MACzBgB,MAgEP,SAASQ,EAASC,GAChB,GAAIA,EAAKC,SACP,OAAOC,QAAQC,OAAO,IAAIzB,UAAU,iBAEtCsB,EAAKC,UAAW,EAGlB,SAASG,EAAgBC,GACvB,OAAO,IAAIH,SAAQ,SAASI,EAASH,GACnCE,EAAOE,OAAS,WACdD,EAAQD,EAAOG,SAEjBH,EAAOI,QAAU,WACfN,EAAOE,EAAOK,WAKpB,SAASC,EAAsBC,GAC7B,IAAIP,EAAS,IAAIQ,WACbC,EAAUV,EAAgBC,GAE9B,OADAA,EAAOU,kBAAkBH,GAClBE,EAoBT,SAASE,EAAYC,GACnB,GAAIA,EAAIC,MACN,OAAOD,EAAIC,MAAM,GAEjB,IAAIC,EAAO,IAAIC,WAAWH,EAAII,YAE9B,OADAF,EAAKG,IAAI,IAAIF,WAAWH,IACjBE,EAAKI,OAIhB,SAASC,IA0FP,OAzFAjC,KAAKU,UAAW,EAEhBV,KAAKkC,UAAY,SAASzB,GAhM5B,IAAoBhC,EAiMhBuB,KAAKmC,UAAY1B,EACZA,EAEsB,iBAATA,EAChBT,KAAKoC,UAAY3B,EACRzC,GAAgBG,KAAKS,UAAUyD,cAAc5B,GACtDT,KAAKsC,UAAY7B,EACRzC,GAAoBuE,SAAS3D,UAAUyD,cAAc5B,GAC9DT,KAAKwC,cAAgB/B,EACZzC,GAAwByE,gBAAgB7D,UAAUyD,cAAc5B,GACzET,KAAKoC,UAAY3B,EAAK5B,WACbb,GAAuBA,KA5MlBS,EA4M6CgC,IA3MjDiC,SAAS9D,UAAUyD,cAAc5D,KA4M3CuB,KAAK2C,iBAAmBlB,EAAYhB,EAAKuB,QAEzChC,KAAKmC,UAAY,IAAIhE,KAAK,CAAC6B,KAAK2C,oBACvB3E,IAAwBO,YAAYK,UAAUyD,cAAc5B,IAASnC,EAAkBmC,IAChGT,KAAK2C,iBAAmBlB,EAAYhB,GAEpCT,KAAKoC,UAAY3B,EAAO9B,OAAOC,UAAUC,SAASC,KAAK2B,GAhBvDT,KAAKoC,UAAY,GAmBdpC,KAAKD,QAAQ6C,IAAI,kBACA,iBAATnC,EACTT,KAAKD,QAAQgC,IAAI,eAAgB,4BACxB/B,KAAKsC,WAAatC,KAAKsC,UAAUO,KAC1C7C,KAAKD,QAAQgC,IAAI,eAAgB/B,KAAKsC,UAAUO,MACvC7E,GAAwByE,gBAAgB7D,UAAUyD,cAAc5B,IACzET,KAAKD,QAAQgC,IAAI,eAAgB,qDAKnC/D,IACFgC,KAAKqB,KAAO,WACV,IAAIyB,EAAWtC,EAASR,MACxB,GAAI8C,EACF,OAAOA,EAGT,GAAI9C,KAAKsC,UACP,OAAO3B,QAAQI,QAAQf,KAAKsC,WACvB,GAAItC,KAAK2C,iBACd,OAAOhC,QAAQI,QAAQ,IAAI5C,KAAK,CAAC6B,KAAK2C,oBACjC,GAAI3C,KAAKwC,cACd,MAAM,IAAIO,MAAM,wCAEhB,OAAOpC,QAAQI,QAAQ,IAAI5C,KAAK,CAAC6B,KAAKoC,cAI1CpC,KAAKgD,YAAc,WACjB,OAAIhD,KAAK2C,iBACAnC,EAASR,OAASW,QAAQI,QAAQf,KAAK2C,kBAEvC3C,KAAKqB,OAAO4B,KAAK7B,KAK9BpB,KAAKkD,KAAO,WACV,IA3FoB7B,EAClBP,EACAS,EAyFEuB,EAAWtC,EAASR,MACxB,GAAI8C,EACF,OAAOA,EAGT,GAAI9C,KAAKsC,UACP,OAjGkBjB,EAiGIrB,KAAKsC,UAhG3BxB,EAAS,IAAIQ,WACbC,EAAUV,EAAgBC,GAC9BA,EAAOqC,WAAW9B,GACXE,EA8FE,GAAIvB,KAAK2C,iBACd,OAAOhC,QAAQI,QA5FrB,SAA+BW,GAI7B,IAHA,IAAIE,EAAO,IAAIC,WAAWH,GACtB0B,EAAQ,IAAIhD,MAAMwB,EAAKyB,QAElBC,EAAI,EAAGA,EAAI1B,EAAKyB,OAAQC,IAC/BF,EAAME,GAAKrE,OAAOsE,aAAa3B,EAAK0B,IAEtC,OAAOF,EAAMI,KAAK,IAqFSC,CAAsBzD,KAAK2C,mBAC7C,GAAI3C,KAAKwC,cACd,MAAM,IAAIO,MAAM,wCAEhB,OAAOpC,QAAQI,QAAQf,KAAKoC,YAI5BpE,IACFgC,KAAK0D,SAAW,WACd,OAAO1D,KAAKkD,OAAOD,KAAKU,KAI5B3D,KAAK4D,KAAO,WACV,OAAO5D,KAAKkD,OAAOD,KAAKY,KAAKC,QAGxB9D,KA1MTF,EAAQlB,UAAUuB,OAAS,SAASnB,EAAMM,GACxCN,EAAOD,EAAcC,GACrBM,EAAQD,EAAeC,GACvB,IAAIyE,EAAW/D,KAAKC,IAAIjB,GACxBgB,KAAKC,IAAIjB,GAAQ+E,EAAWA,EAAW,KAAOzE,EAAQA,GAGxDQ,EAAQlB,UAAkB,OAAI,SAASI,UAC9BgB,KAAKC,IAAIlB,EAAcC,KAGhCc,EAAQlB,UAAUgE,IAAM,SAAS5D,GAE/B,OADAA,EAAOD,EAAcC,GACdgB,KAAKgE,IAAIhF,GAAQgB,KAAKC,IAAIjB,GAAQ,MAG3Cc,EAAQlB,UAAUoF,IAAM,SAAShF,GAC/B,OAAOgB,KAAKC,IAAIgE,eAAelF,EAAcC,KAG/Cc,EAAQlB,UAAUmD,IAAM,SAAS/C,EAAMM,GACrCU,KAAKC,IAAIlB,EAAcC,IAASK,EAAeC,IAGjDQ,EAAQlB,UAAUsB,QAAU,SAASgE,EAAUC,GAC7C,IAAK,IAAInF,KAAQgB,KAAKC,IAChBD,KAAKC,IAAIgE,eAAejF,IAC1BkF,EAASpF,KAAKqF,EAASnE,KAAKC,IAAIjB,GAAOA,EAAMgB,OAKnDF,EAAQlB,UAAUwF,KAAO,WACvB,IAAI5E,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,EAAON,GAC3BQ,EAAM6E,KAAKrF,MAENO,EAAYC,IAGrBM,EAAQlB,UAAU0F,OAAS,WACzB,IAAI9E,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,GACpBE,EAAM6E,KAAK/E,MAENC,EAAYC,IAGrBM,EAAQlB,UAAU2F,QAAU,WAC1B,IAAI/E,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,EAAON,GAC3BQ,EAAM6E,KAAK,CAACrF,EAAMM,OAEbC,EAAYC,IAGjBxB,IACF8B,EAAQlB,UAAUV,OAAOuB,UAAYK,EAAQlB,UAAU2F,SAqJzD,IAAIC,EAAU,CAAC,SAAU,MAAO,OAAQ,UAAW,OAAQ,OAOpD,SAASC,EAAQC,EAAOC,GAE7B,IAPuBC,EACnBC,EAMApE,GADJkE,EAAUA,GAAW,IACFlE,KAEnB,GAAIiE,aAAiBD,EAAS,CAC5B,GAAIC,EAAMhE,SACR,MAAM,IAAIvB,UAAU,gBAEtBa,KAAK8E,IAAMJ,EAAMI,IACjB9E,KAAK+E,YAAcL,EAAMK,YACpBJ,EAAQ5E,UACXC,KAAKD,QAAU,IAAID,EAAQ4E,EAAM3E,UAEnCC,KAAK4E,OAASF,EAAME,OACpB5E,KAAKgF,KAAON,EAAMM,KAClBhF,KAAKiF,OAASP,EAAMO,OACfxE,GAA2B,MAAnBiE,EAAMvC,YACjB1B,EAAOiE,EAAMvC,UACbuC,EAAMhE,UAAW,QAGnBV,KAAK8E,IAAM7F,OAAOyF,GAYpB,GATA1E,KAAK+E,YAAcJ,EAAQI,aAAe/E,KAAK+E,aAAe,eAC1DJ,EAAQ5E,SAAYC,KAAKD,UAC3BC,KAAKD,QAAU,IAAID,EAAQ6E,EAAQ5E,UAErCC,KAAK4E,QAjCkBA,EAiCOD,EAAQC,QAAU5E,KAAK4E,QAAU,MAhC3DC,EAAUD,EAAOM,cACdV,EAAQ9F,QAAQmG,IAAY,EAAIA,EAAUD,GAgCjD5E,KAAKgF,KAAOL,EAAQK,MAAQhF,KAAKgF,MAAQ,KACzChF,KAAKiF,OAASN,EAAQM,QAAUjF,KAAKiF,OACrCjF,KAAKmF,SAAW,MAEK,QAAhBnF,KAAK4E,QAAoC,SAAhB5E,KAAK4E,SAAsBnE,EACvD,MAAM,IAAItB,UAAU,6CAEtBa,KAAKkC,UAAUzB,GAOjB,SAASkD,EAAOlD,GACd,IAAI2E,EAAO,IAAI7C,SAYf,OAXA9B,EACG4E,OACAC,MAAM,KACNpF,SAAQ,SAASqF,GAChB,GAAIA,EAAO,CACT,IAAID,EAAQC,EAAMD,MAAM,KACpBtG,EAAOsG,EAAM3F,QAAQ6F,QAAQ,MAAO,KACpClG,EAAQgG,EAAM9B,KAAK,KAAKgC,QAAQ,MAAO,KAC3CJ,EAAKjF,OAAOsF,mBAAmBzG,GAAOyG,mBAAmBnG,QAGxD8F,EAqBF,SAASM,EAASC,EAAUhB,GAC5BA,IACHA,EAAU,IAGZ3E,KAAK6C,KAAO,UACZ7C,KAAK4F,YAA4B/F,IAAnB8E,EAAQiB,OAAuB,IAAMjB,EAAQiB,OAC3D5F,KAAK6F,GAAK7F,KAAK4F,QAAU,KAAO5F,KAAK4F,OAAS,IAC9C5F,KAAK8F,WAAa,eAAgBnB,EAAUA,EAAQmB,WAAa,KACjE9F,KAAKD,QAAU,IAAID,EAAQ6E,EAAQ5E,SACnCC,KAAK8E,IAAMH,EAAQG,KAAO,GAC1B9E,KAAKkC,UAAUyD,GAjDjBlB,EAAQ7F,UAAUmH,MAAQ,WACxB,OAAO,IAAItB,EAAQzE,KAAM,CAACS,KAAMT,KAAKmC,aAmCvCF,EAAKnD,KAAK2F,EAAQ7F,WAgBlBqD,EAAKnD,KAAK4G,EAAS9G,WAEnB8G,EAAS9G,UAAUmH,MAAQ,WACzB,OAAO,IAAIL,EAAS1F,KAAKmC,UAAW,CAClCyD,OAAQ5F,KAAK4F,OACbE,WAAY9F,KAAK8F,WACjB/F,QAAS,IAAID,EAAQE,KAAKD,SAC1B+E,IAAK9E,KAAK8E,OAIdY,EAASvE,MAAQ,WACf,IAAI6E,EAAW,IAAIN,EAAS,KAAM,CAACE,OAAQ,EAAGE,WAAY,KAE1D,OADAE,EAASnD,KAAO,QACTmD,GAGT,IAAIC,EAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,KAE5CP,EAASQ,SAAW,SAASpB,EAAKc,GAChC,IAA0C,IAAtCK,EAAiBvH,QAAQkH,GAC3B,MAAM,IAAIO,WAAW,uBAGvB,OAAO,IAAIT,EAAS,KAAM,CAACE,OAAQA,EAAQ7F,QAAS,CAACqG,SAAUtB,qBAGvC7G,EAAKoI,aAC/B,IACE,IAAIA,eACJ,MAAOC,GACPD,eAAe,SAASE,EAASvH,GAC/BgB,KAAKuG,QAAUA,EACfvG,KAAKhB,KAAOA,EACZ,IAAImC,EAAQ4B,MAAMwD,GAClBvG,KAAKwG,MAAQrF,EAAMqF,OAErBH,eAAazH,UAAYD,OAAO8H,OAAO1D,MAAMnE,WAC7CyH,eAAazH,UAAU8H,YAAcL,eAGhC,SAASM,EAAMjC,EAAOkC,GAC3B,OAAO,IAAIjG,SAAQ,SAASI,EAASH,GACnC,IAAIiG,EAAU,IAAIpC,EAAQC,EAAOkC,GAEjC,GAAIC,EAAQ5B,QAAU4B,EAAQ5B,OAAO6B,QACnC,OAAOlG,EAAO,IAAIyF,eAAa,UAAW,eAG5C,IAAIU,EAAM,IAAIC,eAEd,SAASC,IACPF,EAAIG,QAGNH,EAAI/F,OAAS,WACX,IAxFgBmG,EAChBpH,EAuFI4E,EAAU,CACZiB,OAAQmB,EAAInB,OACZE,WAAYiB,EAAIjB,WAChB/F,SA3FcoH,EA2FQJ,EAAIK,yBAA2B,GA1FvDrH,EAAU,IAAID,EAGQqH,EAAW3B,QAAQ,eAAgB,KACzCF,MAAM,SAASpF,SAAQ,SAASmH,GAClD,IAAIC,EAAQD,EAAK/B,MAAM,KACnBiC,EAAMD,EAAM3H,QAAQ0F,OACxB,GAAIkC,EAAK,CACP,IAAIjI,EAAQgI,EAAM9D,KAAK,KAAK6B,OAC5BtF,EAAQI,OAAOoH,EAAKjI,OAGjBS,IAgFH4E,EAAQG,IAAM,gBAAiBiC,EAAMA,EAAIS,YAAc7C,EAAQ5E,QAAQ6C,IAAI,iBAC3E,IAAInC,EAAO,aAAcsG,EAAMA,EAAIf,SAAWe,EAAIU,aAClD1G,EAAQ,IAAI2E,EAASjF,EAAMkE,KAG7BoC,EAAI7F,QAAU,WACZN,EAAO,IAAIzB,UAAU,4BAGvB4H,EAAIW,UAAY,WACd9G,EAAO,IAAIzB,UAAU,4BAGvB4H,EAAIY,QAAU,WACZ/G,EAAO,IAAIyF,eAAa,UAAW,gBAGrCU,EAAIa,KAAKf,EAAQjC,OAAQiC,EAAQ/B,KAAK,GAEV,YAAxB+B,EAAQ9B,YACVgC,EAAIc,iBAAkB,EACW,SAAxBhB,EAAQ9B,cACjBgC,EAAIc,iBAAkB,GAGpB,iBAAkBd,GAAO/I,IAC3B+I,EAAIe,aAAe,QAGrBjB,EAAQ9G,QAAQG,SAAQ,SAASZ,EAAON,GACtC+H,EAAIgB,iBAAiB/I,EAAMM,MAGzBuH,EAAQ5B,SACV4B,EAAQ5B,OAAO+C,iBAAiB,QAASf,GAEzCF,EAAIkB,mBAAqB,WAEA,IAAnBlB,EAAImB,YACNrB,EAAQ5B,OAAOkD,oBAAoB,QAASlB,KAKlDF,EAAIqB,UAAkC,IAAtBvB,EAAQ1E,UAA4B,KAAO0E,EAAQ1E,cAIvEwE,EAAM0B,UAAW,EAEZpK,EAAK0I,QACR1I,EAAK0I,MAAQA,EACb1I,EAAK6B,QAAUA,EACf7B,EAAKwG,QAAUA,EACfxG,EAAKyH,SAAWA"}