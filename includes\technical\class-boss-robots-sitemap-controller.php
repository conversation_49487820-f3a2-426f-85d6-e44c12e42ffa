<?php
/**
 * Contrôleur pour la gestion des robots.txt et sitemaps.
 *
 * Cette classe gère les routes API pour les fonctionnalités de robots.txt et sitemaps.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Sitemap_Controller {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'option pour le contenu du robots.txt.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $robots_option    L'option pour le contenu du robots.txt.
     */
    protected $robots_option;

    /**
     * L'option pour les paramètres du sitemap.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $sitemap_option    L'option pour les paramètres du sitemap.
     */
    protected $sitemap_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->robots_option = $plugin_name . '_robots_content';
        $this->sitemap_option = $plugin_name . '_advanced_sitemap_settings';
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Route pour le contenu du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour la validation du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/validate-robots',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'validate_robots_content' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les règles prédéfinies du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots-rules',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_robots_rules' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les paramètres du sitemap avancé
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour régénérer le sitemap
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/regenerate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_advanced_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour l'historique des générations de sitemaps
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_sitemap_generation_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour ping les moteurs de recherche
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/ping',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les URLs personnalisées
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour les types de contenu
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/content-types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_content_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les taxonomies
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/taxonomies',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_taxonomies' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        // Temporaire : bypass pour debug
        return true;

        // Original : return current_user_can( 'manage_options' );
    }

    /**
     * Récupère le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_content( $request ) {
        $content = get_option( $this->robots_option, $this->get_default_robots_content() );
        return rest_ensure_response( array( 'content' => $content ) );
    }

    /**
     * Enregistre le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_robots_content( $request ) {
        $content = sanitize_textarea_field( $request->get_param( 'content' ) );
        update_option( $this->robots_option, $content );
        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Valide le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function validate_robots_content( $request ) {
        $content = sanitize_textarea_field( $request->get_param( 'content' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Valider le contenu
        $errors = Boss_Robots_Validator::validate( $content );
        
        return rest_ensure_response( array( 'errors' => $errors ) );
    }

    /**
     * Récupère des règles prédéfinies pour le fichier robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_rules( $request ) {
        $type = sanitize_text_field( $request->get_param( 'type' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Récupérer les règles
        $rules = Boss_Robots_Validator::generate_rules( $type );
        
        return rest_ensure_response( array( 'rules' => $rules ) );
    }

    /**
     * Récupère les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_advanced_sitemap_settings( $request ) {
        $default_settings = array(
            'enabled' => true,
            'includedPostTypes' => array( 'post', 'page' ),
            'includedTaxonomies' => array( 'category', 'post_tag' ),
            'defaultChangeFreq' => 'weekly',
            'defaultPriority' => 0.7,
            'includeImages' => true,
            'includeLastMod' => true,
            'enableAutoUpdate' => false,
            'autoUpdateFrequency' => 'daily',
            'enableImageSitemap' => false,
            'enableVideoSitemap' => false,
            'enableStoriesSitemap' => false,
            'enableNewsSitemap' => false,
            'enableCustomSitemap' => false,
            'enableTaxonomySitemaps' => false,
            'enablePostTypeSitemaps' => array(
                'post' => false,
                'page' => false,
                'product' => false
            ),
            'customUrls' => array()
        );

        $settings = get_option( $this->sitemap_option, $default_settings );

        // Fusionner avec les paramètres par défaut pour s'assurer que toutes les clés existent
        $settings = array_merge( $default_settings, $settings );

        return rest_ensure_response( array( 'settings' => $settings ) );
    }

    /**
     * Enregistre les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_advanced_sitemap_settings( $request ) {
        $settings = $request->get_param( 'settings' );
        
        // Sanitize les paramètres
        $sanitized_settings = $this->sanitize_sitemap_settings( $settings );
        
        // Enregistrer les paramètres
        update_option( $this->sitemap_option, $sanitized_settings );
        
        // Planifier la mise à jour automatique si activée
        if ( isset( $sanitized_settings['enableAutoUpdate'] ) && $sanitized_settings['enableAutoUpdate'] ) {
            $this->schedule_sitemap_update( $sanitized_settings['autoUpdateFrequency'] );
        } else {
            $this->unschedule_sitemap_update();
        }
        
        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Régénère le sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function regenerate_advanced_sitemap( $request ) {
        // Version simplifiée pour éviter les erreurs fatales
        try {
            // Test de base - juste retourner un succès pour voir si l'API fonctionne
            return new WP_REST_Response( array(
                'success' => true,
                'message' => 'Test API - Endpoint accessible',
                'timestamp' => current_time( 'mysql' )
            ), 200 );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO - Erreur API: ' . $e->getMessage() );

            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur API: ' . $e->getMessage(),
                'timestamp' => current_time( 'mysql' )
            ), 200 );
        }
    }

    /**
     * Récupère l'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_sitemap_generation_history( $request ) {
        // Inclure la classe utilitaire
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-sitemap-utils.php';
        
        // Récupérer l'historique
        $history = Boss_Sitemap_Utils::get_sitemap_generation_history();
        
        return rest_ensure_response( array( 'history' => $history ) );
    }

    /**
     * Ping les moteurs de recherche pour les informer de la mise à jour du sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function ping_search_engines( $request ) {
        // Inclure la classe utilitaire
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-sitemap-utils.php';
        
        // Ping les moteurs de recherche
        $results = Boss_Sitemap_Utils::ping_search_engines();
        
        return rest_ensure_response( array( 'results' => $results ) );
    }

    /**
     * Récupère les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_custom_urls( $request ) {
        $settings = get_option( $this->sitemap_option, array() );
        $urls = isset( $settings['customUrls'] ) ? $settings['customUrls'] : array();
        
        return rest_ensure_response( array( 'urls' => $urls ) );
    }

    /**
     * Enregistre les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_custom_urls( $request ) {
        $urls = $request->get_param( 'urls' );
        $settings = get_option( $this->sitemap_option, array() );
        
        // Sanitize les URLs
        $sanitized_urls = array();
        foreach ( $urls as $url ) {
            $sanitized_url = array(
                'loc'        => esc_url_raw( $url['loc'] ),
                'lastmod'    => sanitize_text_field( $url['lastmod'] ),
                'changefreq' => sanitize_text_field( $url['changefreq'] ),
                'priority'   => sanitize_text_field( $url['priority'] ),
            );
            $sanitized_urls[] = $sanitized_url;
        }
        
        // Mettre à jour les paramètres
        $settings['customUrls'] = $sanitized_urls;
        update_option( $this->sitemap_option, $settings );
        
        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Récupère les types de contenu disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_content_types( $request ) {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        $content_types = array();
        
        foreach ( $post_types as $post_type ) {
            $content_types[] = array(
                'name'  => $post_type->name,
                'label' => $post_type->label,
            );
        }
        
        return rest_ensure_response( array( 'contentTypes' => $content_types ) );
    }

    /**
     * Récupère les taxonomies disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_taxonomies( $request ) {
        $taxonomies_objects = get_taxonomies( array( 'public' => true ), 'objects' );
        $taxonomies = array();
        
        foreach ( $taxonomies_objects as $taxonomy ) {
            $taxonomies[] = array(
                'name'  => $taxonomy->name,
                'label' => $taxonomy->label,
            );
        }
        
        return rest_ensure_response( array( 'taxonomies' => $taxonomies ) );
    }

    /**
     * Sanitize les paramètres du sitemap.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres du sitemap.
     * @return   array                 Les paramètres sanitizés.
     */
    private function sanitize_sitemap_settings( $settings ) {
        $sanitized_settings = array();
        
        // Sanitize les paramètres booléens
        $boolean_fields = array(
            'enabled',
            'includeImages',
            'includeLastMod',
            'enableAutoUpdate',
            'enableImageSitemap',
            'enableVideoSitemap',
            'enableStoriesSitemap',
            'enableNewsSitemap',
            'enableCustomSitemap',
            'enableTaxonomySitemaps',
        );
        
        foreach ( $boolean_fields as $field ) {
            $sanitized_settings[$field] = isset( $settings[$field] ) ? (bool) $settings[$field] : false;
        }
        
        // Sanitize les paramètres de texte
        $text_fields = array(
            'defaultChangeFreq',
            'autoUpdateFrequency',
        );
        
        foreach ( $text_fields as $field ) {
            $sanitized_settings[$field] = isset( $settings[$field] ) ? sanitize_text_field( $settings[$field] ) : '';
        }
        
        // Sanitize les paramètres numériques
        $sanitized_settings['defaultPriority'] = isset( $settings['defaultPriority'] ) ? floatval( $settings['defaultPriority'] ) : 0.7;
        
        // Sanitize les tableaux
        $array_fields = array(
            'includedPostTypes',
            'includedTaxonomies',
            'newsPostTypes',
        );
        
        foreach ( $array_fields as $field ) {
            $sanitized_settings[$field] = isset( $settings[$field] ) ? array_map( 'sanitize_text_field', $settings[$field] ) : array();
        }
        
        // Sanitize les URLs personnalisées
        if ( isset( $settings['customUrls'] ) && is_array( $settings['customUrls'] ) ) {
            $sanitized_settings['customUrls'] = array();
            
            foreach ( $settings['customUrls'] as $url ) {
                $sanitized_url = array(
                    'loc'        => esc_url_raw( $url['loc'] ),
                    'lastmod'    => sanitize_text_field( $url['lastmod'] ),
                    'changefreq' => sanitize_text_field( $url['changefreq'] ),
                    'priority'   => sanitize_text_field( $url['priority'] ),
                );
                
                $sanitized_settings['customUrls'][] = $sanitized_url;
            }
        } else {
            $sanitized_settings['customUrls'] = array();
        }
        
        return $sanitized_settings;
    }

    /**
     * Planifie la mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     * @param    string    $frequency    La fréquence de mise à jour.
     */
    private function schedule_sitemap_update( $frequency ) {
        // Supprimer la planification existante
        $this->unschedule_sitemap_update();
        
        // Planifier la mise à jour
        if ( $frequency === 'hourly' ) {
            wp_schedule_event( time(), 'hourly', 'boss_seo_update_sitemaps' );
        } elseif ( $frequency === 'daily' ) {
            wp_schedule_event( time(), 'daily', 'boss_seo_update_sitemaps' );
        } elseif ( $frequency === 'weekly' ) {
            wp_schedule_event( time(), 'weekly', 'boss_seo_update_sitemaps' );
        }
    }

    /**
     * Supprime la planification de mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     */
    private function unschedule_sitemap_update() {
        $timestamp = wp_next_scheduled( 'boss_seo_update_sitemaps' );
        
        if ( $timestamp ) {
            wp_unschedule_event( $timestamp, 'boss_seo_update_sitemaps' );
        }
    }

    /**
     * Récupère le contenu par défaut du robots.txt.
     *
     * @since    1.2.0
     * @return   string    Le contenu par défaut du robots.txt.
     */
    private function get_default_robots_content() {
        $site_url = get_site_url();
        
        $content = "User-agent: *\n";
        $content .= "Disallow: /wp-admin/\n";
        $content .= "Allow: /wp-admin/admin-ajax.php\n";
        $content .= "Disallow: /wp-includes/\n";
        $content .= "Disallow: /wp-content/plugins/\n";
        $content .= "Disallow: /wp-login.php\n";
        $content .= "Disallow: /xmlrpc.php\n";
        $content .= "Disallow: /readme.html\n\n";
        $content .= "Sitemap: {$site_url}/sitemap.xml\n";
        
        return $content;
    }
}
